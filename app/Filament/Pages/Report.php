<?php

namespace App\Filament\Pages;

use App\Models\Loan;
use App\Models\Student;
use BackedEnum;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Page;
use Filament\Schemas\Components\Form;
use Filament\Support\Icons\Heroicon;

class Report extends Page
{
    protected string $view = 'filament.pages.report';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedNewspaper;

    protected static string $routePath = '/report';

    protected ?Form $form = null;

    public $studentId;

    public $studentName;

    public $classroom;

    public $major;

    public $loans = [];

    public function mount($studentId = null)
    {
        if ($studentId) {
            $this->studentId = $studentId;
            $student = Student::with(['classroom', 'major'])->findOrFail($studentId);
            $this->studentName = $student->name;
            $this->classroom = $student->classroom->name;
            $this->major = $student->major->name;
            $this->loans = Loan::with('book')
                ->where('student_id', $studentId)
                ->get()
                ->map(function ($loan) {
                    return [
                        'book_name' => $loan->book->name,
                        'book_number' => $loan->book->book_number,
                        'loan_date' => $loan->loan_date,
                        'return_date' => $loan->return_date,
                    ];
                })->all();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('studentName')
                    ->label('Nama')
                    ->disabled(),
                TextInput::make('classroom')
                    ->label('Kelas')
                    ->disabled(),
                TextInput::make('major')
                    ->label('Jurusan')
                    ->disabled(),
                Repeater::make('loans')
                    ->label('Daftar Buku')
                    ->schema([
                        TextInput::make('book_name')
                            ->label('Nama Buku')
                            ->disabled(),
                        TextInput::make('book_number')
                            ->label('No Buku')
                            ->disabled(),
                        TextInput::make('loan_date')
                            ->label('Tanggal Ambil & Paraf')
                            ->disabled(),
                        TextInput::make('return_date')
                            ->label('Tanggal Kembali & Paraf')
                            ->disabled(),
                    ])
                    ->defaultItems(0)
                    ->disabled(),
            ])
            ->statePath('data')
            ->model(null);
    }
}
