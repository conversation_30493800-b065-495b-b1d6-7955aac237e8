<?php

namespace App\Filament\Resources\Books\Schemas;

use Dom\Text;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class BookForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make("name")
                    ->label("Nama Buku")
                    ->required()
                    ->unique(),
                TextInput::make("book_number")
                    ->label("Nomor Buku")
                    ->numeric()
                    ->required()
                    ->unique(),
                TextInput::make("author")
                    ->label("Penulis")
                    ->required(),
                TextInput::make("publisher")
                    ->label("Penerbit")
                    ->required(),
                TextInput::make("year")
                    ->label("Tahun Terbit")
                    ->required()
                    ->numeric()
                    ->minValue(2000)
                    ->maxValue(date("Y"))
                    ->regex('/^\d{4}$/'),
                TextInput::make("isbn")
                    ->label("ISBN")
                    ->required()
                    ->unique('books', 'isbn')
                    ->regex('/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3,4})[- 0-9]{13,17}$)[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/')
                    ->maxLength(17)                                                            
                    ->helperText('Masukkan ISBN-10 atau ISBN-13, misalnya 978-3-16-148410-0 atau 123456789X.'),
                Select::make("level")
                    ->label("Tingkat")
                    ->options([
                        'X' => 'X',
                        'XI' => 'XI',
                        'XII' => 'XII'
                        ])
                    ->required(),
                TextInput::make("buy_year")
                    ->label("Tahun Beli")
                    ->required()
                    ->numeric()
                    ->minValue(2000)
                    ->maxValue(date("Y"))
                    ->regex('/^\d{4}$/'),
                TextInput::make('stock')
                    ->label("Stok")
                    ->required()
                    ->numeric()                    
            ]);
    }
}
