<?php

namespace App\Filament\Resources\Books\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class BookInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Details')
                    ->inlineLabel()
                    ->schema([
                        TextEntry::make("name")
                            ->label("Nama Buku"),
                        TextEntry::make("book_number")
                            ->label("Nomor Buku"),
                        TextEntry::make("author")
                            ->label("Penulis"),
                        TextEntry::make("publisher")
                            ->label("Penerbit"),
                        TextEntry::make("year")
                            ->label("Tahun Terbit"),
                        TextEntry::make("isbn")
                            ->label("ISBN"),
                        TextEntry::make("level")
                            ->label("Tingkat"),
                        TextEntry::make("buy_year")
                            ->label("Tahun Beli"),
                        TextEntry::make("stock")
                            ->label("Stok")
                    ])->columnSpan(['default' => 'full'])
            ]);
    }
}
