<?php

namespace App\Filament\Resources\Books\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class BooksTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make("name")
                    ->label("Nama Buku")
                    ->searchable(),
                TextColumn::make("book_number")
                    ->label("Nomor Buku")
                    ->searchable(),
                TextColumn::make("author")
                    ->label("Penulis")
                    ->searchable(),
                TextColumn::make("publisher")
                    ->label("Penerbit")
                    ->searchable(),
                TextColumn::make("year")
                    ->label("Tahun Terbit")
                    ->searchable(),
                TextColumn::make("isbn")
                    ->label("ISBN")
                    ->searchable(),
                TextColumn::make("level")
                    ->label("Tingkat")
                    ->searchable(),
                TextColumn::make("buy_year")
                    ->label("Tahun Beli")
                    ->searchable(),
                TextColumn::make("stock")
                    ->label("Stok")
                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make("level")
                    ->label("Tingkat")    
                    ->options([
                        'X' => 'X',
                        'XI' => 'XI',
                        'XII' => 'XII'
                    ]),                        
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
