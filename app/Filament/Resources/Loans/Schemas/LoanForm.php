<?php

namespace App\Filament\Resources\Loans\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Schemas\Schema;

class LoanForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make("student_id")
                    ->relationship("student", "name")
                    ->searchable()
                    ->required()
                    ->loadingMessage('Loading students...'),
                Select::make("book_id")
                    ->relationship("book", "name")
                    ->searchable()
                    ->required()
                    ->loadingMessage('Loading books...'),
                DatePicker::make('loan_date')
                    ->label('Tanggal Ambil')
                    ->required(),
                DatePicker::make('return_date')
                    ->label('Tanggal Kembali'),
            ]);
    }
}
