<?php

namespace App\Filament\Resources\Loans\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class LoansTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make("student.name")
                    ->label("Nama Siswa")
                    ->searchable(),
                TextColumn::make("book.name")
                    ->label("Nama Buku")
                    ->searchable(),
                TextColumn::make("loan_date")
                    ->label("Tanggal Pinjam")
                    ->searchable(),
                TextColumn::make("return_date")
                    ->label("Tanggal Kembali")
                    ->searchable(),
                TextColumn::make("Status")
                    ->label("Status")
                    ->getStateUsing(function ($record) {
                        if (!is_null($record->return_date)) {
                            return 'Dikembalikan';
                        }
                        if (!is_null($record->loan_date)) {
                            return 'Dipinjam';
                        }
                        return 'Tidak Diketahui';
                    })
                    ->badge()
                    ->colors([
                        'success' => 'Dikembalikan',
                        'primary' => 'Dipinjam',
                        'warning' => 'Tidak Diketahui',
                    ])
                    
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
