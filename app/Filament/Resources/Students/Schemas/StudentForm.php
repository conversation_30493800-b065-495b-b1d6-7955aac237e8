<?php

namespace App\Filament\Resources\Students\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class StudentForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->required()
                    ->unique(),
                Select::make('classroom_id')
                    ->relationship('classroom', 'name')
                    ->required(),
                Select::make('major_id')
                    ->relationship('major', 'name')
                    ->required(),
            ]);
    }
}
