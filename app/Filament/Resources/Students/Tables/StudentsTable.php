<?php

namespace App\Filament\Resources\Students\Tables;

use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class StudentsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('classroom.name')
                    ->label('Kelas'),
                TextColumn::make('major.name')
                    ->label('<PERSON>rusan'),
            ])
            ->filters([
                SelectFilter::make('classroom')
                    ->relationship('classroom', 'name')
                    ->label('Kelas'),
            ])
            ->recordActions([
                Action::make('viewReport')
                    ->label('View Report')
                    ->url(fn ($record) => route('filament.pages.report', ['studentId' => $record->id]))
                    ->openUrlInNewTab(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
