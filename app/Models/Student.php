<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = [
        'name',
        'classroom_id',
        'major_id',
    ];

    public function classroom()
    {
        return $this->belongsTo(Classroom::class);
    }

    public function major()
    {
        return $this->belongsTo(Major::class);
    }

    public function loans(){
        return $this->hasMany(Loan::class);
    }
}
