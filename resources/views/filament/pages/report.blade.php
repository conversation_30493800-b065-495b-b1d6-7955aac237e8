<x-filament-panels::page>
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-4 text-center">DAFTAR TANDA TERIMA DAN PENGEMBALIAN BUKU</h1>
        <h2 class="text-xl mb-2 text-center">SMKS Real Informatika</h2>
        <p class="mb-4 text-center">TAHUN PELAJARAN: {{ now()->year }}/{{ now()->addYear()->year }}</p>       

        @if($this->studentName)
            <div class="mb-6 p-4 bg-gray-50 rounded">
                <p><strong>Nama:</strong> {{ $this->studentName }}</p>
                <p><strong>Kelas:</strong> {{ $this->classroom }}</p>
                <p><strong>Jurusan:</strong> {{ $this->major }}</p>
            </div>
        @endif

        <table class="w-full border-collapse mt-4">
            <thead>
                <tr class="bg-green-200">
                    <th class="border p-2">NO</th>
                    <th class="border p-2">NAMA BUKU</th>
                    <th class="border p-2">NO BUKU</th>
                    <th class="border p-2">TANGGAL AMBIL & PARAF</th>
                    <th class="border p-2">TANGGAL KEMBALI & PARAF</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($this->loans as $index => $loan)
                    <tr>
                        <td class="border p-2">{{ $index + 1 }}</td>
                        <td class="border p-2">{{ $loan['book_name'] }}</td>
                        <td class="border p-2">{{ $loan['book_number'] }}</td>
                        <td class="border p-2">{{ $loan['loan_date'] }}</td>
                        <td class="border p-2">{{ $loan['return_date'] ?? '' }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="border p-2 text-center text-gray-500">
                            @if($this->studentId)
                                Tidak ada data peminjaman untuk siswa ini.
                            @else
                                Pilih siswa untuk melihat data peminjaman.
                            @endif
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <div class="mt-4 ">
            <p>Batam: __________________</p>
            <p>Kepala Perpustakaan</p>
        </div>
    </div>
</x-filament-panels::page>