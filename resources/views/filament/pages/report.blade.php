<x-filament-panels::page>
    <div class="p-6 bg-white">
        <!-- Print Button - Hidden when printing -->
        <div class="mb-4 print-hidden">
            <button
                onclick="printReport()"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-flex items-center"
            >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print Report
            </button>
        </div>

        <h1 class="text-2xl font-bold mb-4 text-center">DAFTAR TANDA TERIMA DAN PENGEMBALIAN BUKU</h1>
        <h2 class="text-xl mb-2 text-center">SMKS Real Informatika</h2>
        <p class="mb-4 text-center">TAHUN PELAJARAN: {{ now()->year }}/{{ now()->addYear()->year }}</p>

        @if($this->studentName)
            <div class="mb-6 p-4 bg-gray-50 rounded">
                <p><strong>Nama:</strong> {{ $this->studentName }}</p>
                <p><strong>Kelas:</strong> {{ $this->classroom }}</p>
                <p><strong>Jurusan:</strong> {{ $this->major }}</p>
            </div>
        @endif

        <table class="w-full border-collapse mt-4">
            <thead>
                <tr class="bg-green-200">
                    <th class="border p-2">NO</th>
                    <th class="border p-2">NAMA BUKU</th>
                    <th class="border p-2">NO BUKU</th>
                    <th class="border p-2">TANGGAL AMBIL & PARAF</th>
                    <th class="border p-2">TANGGAL KEMBALI & PARAF</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($this->loans as $index => $loan)
                    <tr>
                        <td class="border p-2">{{ $index + 1 }}</td>
                        <td class="border p-2">{{ $loan['book_name'] }}</td>
                        <td class="border p-2">{{ $loan['book_number'] }}</td>
                        <td class="border p-2">{{ $loan['loan_date'] }}</td>
                        <td class="border p-2">{{ $loan['return_date'] ?? '' }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="border p-2 text-center text-gray-500">
                            @if($this->studentId)
                                Tidak ada data peminjaman untuk siswa ini.
                            @else
                                Pilih siswa untuk melihat data peminjaman.
                            @endif
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <div class="mt-4 text-right">
            <p>Batam: __________________</p>
            <p>Kepala Perpustakaan</p>
            <br>
            <br>
            <br>
            <p>______________________</p>
        </div>
    </div>

    <!-- Print-specific styles -->
    <style>
        @media print {
            /* Hide print button */
            .print-hidden {
                display: none !important;
            }

            /* Hide Filament sidebar and navigation */
            .fi-sidebar {
                display: none !important;
            }

            /* Hide Filament topbar */
            .fi-topbar {
                display: none !important;
            }

            /* Remove any overlays or modals */
            .fi-modal,
            .fi-overlay,
            [data-modal-backdrop],
            .backdrop,
            .overlay {
                display: none !important;
            }

            /* Ensure full white background */
            html, body {
                background: white !important;
                background-color: white !important;
            }

            /* Adjust main content to take full width with white background */
            .fi-main {
                margin-left: 0 !important;
                background: white !important;
                background-color: white !important;
            }

            /* Ensure page content has white background */
            .fi-page,
            .fi-page-content {
                background: white !important;
                background-color: white !important;
            }

            /* Ensure the report container has white background */
            .p-6 {
                background: white !important;
                background-color: white !important;
            }

            /* Ensure table borders show in print */
            table, th, td {
                border: 1px solid #000 !important;
            }

            /* Preserve background colors for specific elements */
            .bg-green-200 {
                background-color: #d1fae5 !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .bg-gray-50 {
                background-color: #f9fafb !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
        }
    </style>

    <script>
        function printReport() {
            window.print();
        }
    </script>
</x-filament-panels::page>