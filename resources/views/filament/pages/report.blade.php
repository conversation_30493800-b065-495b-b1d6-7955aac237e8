<x-filament-panels::page>
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-4">DAFTAR TANDA TERIMA DAN PENGEMBALIAN BUKU</h1>
        <h2 class="text-xl mb-2">SMKS Real Informatika</h2>
        <p class="mb-4">TAHUN PELAJARAN: {{ now()->year }}/{{ now()->addYear()->year }}</p>

        <form wire:submit.prevent="submit">
            {{ $this->form }}

            <table class="w-full border-collapse mt-4">
                <thead>
                    <tr class="bg-green-200">
                        <th class="border p-2">NO</th>
                        <th class="border p-2">NAMA BUKU</th>
                        <th class="border p-2">NO BUKU</th>
                        <th class="border p-2">TANGGAL AMBIL & PARAF</th>
                        <th class="border p-2">TANGGAL KEMBALI & PARAF</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($this->loans as $index => $loan)
                        <tr>
                            <td class="border p-2">{{ $index + 1 }}</td>
                            <td class="border p-2">{{ $loan['book_name'] }}</td>
                            <td class="border p-2">{{ $loan['book_number'] }}</td>
                            <td class="border p-2">{{ $loan['loan_date'] }}</td>
                            <td class="border p-2">{{ $loan['return_date'] ?? '' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <div class="mt-4">
                <p>Batam: __________________</p>
                <p>Kepala Perpustakaan</p>
            </div>
        </form>
    </div>
</x-filament-panels::page>